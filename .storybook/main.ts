import type { StorybookConfig } from "@storybook/preact-vite";
import { mergeConfig } from "vite";

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@chromatic-com/storybook",
    "@storybook/addon-docs",
    "@storybook/addon-a11y",
    "@storybook/addon-vitest",
  ],
  framework: {
    name: "@storybook/preact-vite",
    options: {},
  },
  viteFinal: async (config) => {
    return mergeConfig(config, {
      esbuild: {
        jsx: "automatic",
        jsxImportSource: "preact",
      },
      resolve: {
        alias: {
          react: "preact/compat",
          "react-dom": "preact/compat",
        },
      },
    });
  },
};
export default config;
