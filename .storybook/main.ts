import type { StorybookConfig } from "@storybook/preact-vite";
import { mergeConfig } from "vite";

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [
    "@chromatic-com/storybook",
    "@storybook/addon-docs",
    "@storybook/addon-a11y",
    "@storybook/addon-vitest",
  ],
  framework: {
    name: "@storybook/preact-vite",
    options: {
      builder: {
        viteConfigPath: undefined,
      },
    },
  },
  viteFinal: async (config) => {
    return mergeConfig(config, {
      esbuild: {
        jsx: "automatic",
        jsxImportSource: "preact",
      },
      resolve: {
        alias: {
          react: "preact/compat",
          "react-dom": "preact/compat",
        },
      },
      define: {
        global: "globalThis",
      },
      optimizeDeps: {
        include: ["preact/hooks", "preact/compat"],
      },
    });
  },
};
export default config;
