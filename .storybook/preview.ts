import type { Preview } from "@storybook/preact-vite";

// Ensure Preact hooks work correctly in Storybook
if (typeof window !== "undefined") {
  // Force Preact to use the correct hooks implementation
  (window as any).__PREACT_DEVTOOLS__ = {
    attachPreact: () => {},
  };
}

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
