import {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
  type HTMLAttributes,
  type PropsWithChildren,
} from "preact/compat";
import { animate } from "motion";
import { useKeyDown } from "../hooks";

interface DrawerProps extends HTMLAttributes<HTMLDivElement> {
  isOpen: boolean;
  onOpenChange?: (value: boolean) => void;
  onClosed?: () => void;
  elbowFirst?: boolean;
}
export type DrawerHandle = {
  scrollToTop: () => void;
};

export const Drawer = forwardRef<DrawerHandle, PropsWithChildren<DrawerProps>>(
  ({ children, isOpen, onOpenChange, onClosed, elbowFirst = true }, ref) => {
    const animationElementRef = useRef<HTMLDivElement>(null);
    const [wrapperWidth, setWrapperWidth] = useState<string>();
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
      if (isOpen) {
        if (elbowFirst) {
          setWrapperWidth(undefined);
        }
        setMounted(true);
        setTimeout(() => {
          animate(animationElementRef.current!, {
            transform: ["translateX(100%)", "translateX(0)"],
            opacity: 1,
          }).finished.then(() => {
            if (!elbowFirst) {
              setWrapperWidth(undefined);
            }
          });
        });
      } else {
        setWrapperWidth("0");
        if (!animationElementRef.current) return;
        animate(animationElementRef.current, {
          transform: ["translateX(0)", "translateX(100%)"],
          opacity: 0,
        }).finished.then(() => {
          onClosed?.();
          setMounted(false);
        });
      }
    }, [isOpen]);

    const scrollContainerRef = useRef<HTMLDivElement>(null);
    useImperativeHandle(
      ref,
      () => {
        return {
          scrollToTop() {
            scrollContainerRef.current?.scrollTo({
              top: 0,
            });
          },
        };
      },
      []
    );

    useKeyDown("Escape", () => {
      onOpenChange?.(false);
    });

    if (mounted) {
      return (
        <div className={"sticky top-4"}>
          <div
            className={"relative w-0 lg:w-128"}
            style={{ width: wrapperWidth }}
          >
            <div
              ref={animationElementRef}
              className={
                "fixed top-0 right-0 w-full lg:w-128 bottom-0 left-0 lg:absolute lg:bottom-auto lg:left-auto"
              }
            >
              <div
                className={
                  "bg-zinc-800 h-full lg:rounded-lg overflow-hidden shadow"
                }
              >
                <div
                  ref={scrollContainerRef}
                  className="max-h-[100vh] lg:max-h-[calc(100vh-var(--spacing)*8)] overflow-y-scroll"
                >
                  {children}
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }
  }
);
