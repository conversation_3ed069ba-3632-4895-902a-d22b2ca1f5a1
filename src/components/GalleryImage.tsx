import clsx from "clsx";

interface Props {
  focus?: boolean;
  imageSrc: string;
  description?: string;
  imageSize: {
    width: number;
    height: number;
  };
  className?: string;
  onClick?: () => void;
}

export function GalleryImage({
  imageSize,
  className,
  focus,
  imageSrc,
  description,
  onClick,
  ...rest
}: Props) {
  const expectedImageHeight = 180;
  const expectedImageWidth = Math.min(
    Math.max((expectedImageHeight / imageSize.height) * imageSize.width, 100),
    300
  );
  return (
    <div
      {...rest}
      style={{
        width: `${expectedImageWidth}px`,
      }}
      class={clsx("relative", className)}
      onClick={onClick}
    >
      {/* 选中状态的背景 */}
      <div
        className={clsx(
          "absolute pointer-events-none -left-2 -top-2 h-[calc(100%+1rem)] w-[calc(100%+1rem)] rounded-2xl",
          focus && "bg-gray-700 border border-blue-200"
        )}
      ></div>

      {/* 图片 */}
      <div
        style={{ height: `${expectedImageHeight}px` }}
        className="bg-neutral-800 flex items-center justify-center w-full rounded-2xl overflow-hidden"
      >
        <img
          class="w-full relative"
          src={imageSrc}
          loading="lazy"
          decoding="async"
          alt=""
        />
      </div>

      {/* 文字描述 */}
      {description && (
        <div className="relative text-sm px-2 mt-2 text-gray-400">
          {description}
        </div>
      )}
    </div>
  );
}
