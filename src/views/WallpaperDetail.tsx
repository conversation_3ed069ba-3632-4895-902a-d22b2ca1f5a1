import { useEffect, useState } from "preact/hooks";
import { TextButton } from "../components/Button";
import { ChevronLeft, ChevronRight, CloseIcon } from "../components/icons";
import { SizeEnum } from "../common";
import { downloadImage } from "../api-runtime";
import { IMac, Iphone13ProMax } from "../components/devices/index";

interface Props {
  wallpaper: Wallpaper;
  onClickChevronLeft?: () => void;
  onClickChevronRight?: () => void;
  onClickClose: () => void;
  onDisplayedWallpaperChange: () => void;
}

// Declare the turnstile property on the Window interface
declare global {
  interface Window {
    turnstile: any | undefined;
  }
}

export function WallpaperDetail(props: Props) {
  const [btnText, setBtnText] = useState<string>(); // 非 undfined 状态也用于 loading

  const [similarWallpapers, setSimilarWallpapers] = useState<Wallpaper[]>();

  useEffect(() => {
    setDisplayedWallpaper(props.wallpaper);
    fetch(`/api/similar/${props.wallpaper.id}.json`).then(async (resp) => {
      const data = await resp.json();
      setSimilarWallpapers(data);
    });
  }, [props.wallpaper]);

  const [displayedWallpaper, setDisplayedWallpaper] = useState(props.wallpaper);
  useEffect(() => {
    props.onDisplayedWallpaperChange();
  }, [displayedWallpaper]);

  return (
    <div>
      {/* header */}
      <div className="flex py-3 px-3 *:cursor-pointer space-x-5 text-neutral-400">
        <span className="ml-auto"></span> {/* 占位 */}
        {props.onClickChevronLeft && (
          <button onClick={props.onClickChevronLeft}>
            <ChevronLeft />
          </button>
        )}
        {props.onClickChevronRight && (
          <button onClick={props.onClickChevronRight}>
            <ChevronRight />
          </button>
        )}
        <button onClick={props.onClickClose}>
          <CloseIcon />
        </button>
      </div>

      {/* display image */}
      <div>
        <>
          {/* 图片展示区域 */}
          <div className="flex items-center justify-center h-120 relative overflow-hidden">
            {/* 模糊背景 */}
            <div
              className="absolute top-0 left-0 w-full h-full scale-120"
              style={{
                backgroundImage: `url(${displayedWallpaper.imageUrl})`,
                backgroundPosition: "center",
                backgroundSize: "cover",
                filter: "blur(15px)",
              }}
            ></div>

            {/* 设备 */}
            {displayedWallpaper.width > displayedWallpaper.height ? (
              <IMac backgroundImage={displayedWallpaper.imageUrl} />
            ) : (
              <Iphone13ProMax backgroundImage={displayedWallpaper.imageUrl} />
            )}
          </div>

          <div>
            <p className="text-sm px-2 py-2 text-neutral-400">
              Uploaded by:{" "}
              <span className="underline">{displayedWallpaper.uploader}</span>
            </p>
          </div>

          {/* 下载按钮 */}
          <div className="px-2 py-4 flex items-center flex-col">
            <div id="cf-turnstile" data-theme="dark"></div>
            <div className={"min-w-1/2"}>
              <TextButton
                loading={btnText !== undefined}
                text={btnText || "Download HD Image"}
                onClick={() => {
                  setBtnText("Checking");

                  const turnstileEl = document.getElementById("cf-turnstile")!;
                  // 清除子元素
                  while (turnstileEl.firstChild) {
                    turnstileEl.removeChild(turnstileEl.firstChild);
                  }
                  window.turnstile.render(turnstileEl, {
                    sitekey: import.meta.env.PUBLIC_CF_TURNSTILE_SITEKEY,
                    callback: function (token: string) {
                      setBtnText("Downloading");
                      downloadImage({
                        key: displayedWallpaper.md5,
                        cFTurnstileResponse: token,
                      }).finally(() => {
                        setBtnText(undefined);
                      });
                    },
                  });
                }}
              ></TextButton>
            </div>
          </div>
        </>
      </div>

      {/* related wallpapers */}
      {!!similarWallpapers?.length && (
        <div className="border-t border-neutral-700">
          <ul className="grid grid-cols-2 sm:grid-cols-3 gap-4 p-4">
            {similarWallpapers.map((wallpaper) => {
              return (
                <li
                  key={wallpaper.md5}
                  className="h-36 overflow-hidden rounded-md cursor-pointer"
                  onClick={() => {
                    setDisplayedWallpaper(wallpaper);
                  }}
                >
                  <img
                    className="w-full h-full object-cover"
                    src={wallpaper.imageUrl}
                    alt=""
                  />
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
}
