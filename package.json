{"name": "wallery", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "type-check": "tsc --noEmit", "test": "vitest", "deploy": "wrangler pages deploy ./dist --project-name=wallery", "openapi-generator": "openapi-generator-cli generate  -g typescript-fetch -o ./src/api-client -i", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@astrojs/preact": "^4.1.0", "astro": "^5.10.1", "clsx": "^2.1.1", "motion": "^12.19.1", "p-map": "^7.0.3", "preact": "^10.27.2"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@storybook/addon-a11y": "^9.1.5", "@storybook/addon-docs": "^9.1.5", "@storybook/addon-vitest": "^9.1.5", "@storybook/preact-vite": "^9.1.5", "@tailwindcss/vite": "^4.1.13", "@types/mime-types": "^3.0.1", "mime-types": "^3.0.1", "sass-embedded": "^1.89.2", "storybook": "^9.1.5", "tailwindcss": "^4.1.13", "vitest": "^3.2.4", "wrangler": "^4.22.0"}}